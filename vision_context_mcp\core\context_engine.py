"""
Context Engine for Vision Context MCP Server

Arka planda çalışan olay izleme ve analiz sistemi
"""

import asyncio
import platform
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

import structlog

try:
    import pygetwindow as gw
except ImportError:
    gw = None

try:
    import psutil
except ImportError:
    psutil = None

from ..config.enhanced_settings import get_enhanced_settings
from .screen_capture import ScreenCapture
from .vision_llm import VisionLLM

logger = structlog.get_logger(__name__)


class EventType(Enum):
    """Event types for context monitoring"""
    WINDOW_CHANGE = "window_change"
    APPLICATION_CHANGE = "application_change"
    SCREEN_CHANGE = "screen_change"
    SYSTEM_EVENT = "system_event"
    USER_ACTIVITY = "user_activity"


@dataclass
class ContextEvent:
    """Context event data structure"""
    event_type: EventType
    timestamp: datetime
    data: Dict[str, Any]
    priority: int = 1  # 1=low, 2=medium, 3=high
    processed: bool = False


class ContextEngine:
    """Background context analysis engine"""
    
    def __init__(self):
        self.settings = get_enhanced_settings()
        self.screen_capture = ScreenCapture()
        self.vision_llm = VisionLLM()
        
        # State tracking
        self.is_running = False
        self.current_window = None
        self.current_application = None
        self.last_screen_hash = None
        
        # Event storage
        self.events: List[ContextEvent] = []
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        
        # Monitoring tasks
        self.monitoring_tasks: List[asyncio.Task] = []
        
        logger.info("Context engine initialized")
    
    async def start(self) -> None:
        """Start context monitoring"""
        if self.is_running:
            logger.warning("Context engine already running")
            return
        
        self.is_running = True
        logger.info("Starting context engine")
        
        # Start monitoring tasks
        self.monitoring_tasks = [
            asyncio.create_task(self._monitor_windows()),
            asyncio.create_task(self._monitor_applications()),
            asyncio.create_task(self._monitor_screen_changes()),
            asyncio.create_task(self._process_events()),
        ]
        
        # Add system monitoring if psutil is available
        if psutil:
            self.monitoring_tasks.append(
                asyncio.create_task(self._monitor_system())
            )
        
        logger.info("Context monitoring started", tasks=len(self.monitoring_tasks))
    
    async def stop(self) -> None:
        """Stop context monitoring"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Stopping context engine")
        
        # Cancel all monitoring tasks
        for task in self.monitoring_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        self.monitoring_tasks.clear()
        
        logger.info("Context monitoring stopped")
    
    async def _monitor_windows(self) -> None:
        """Monitor window changes"""
        if not gw:
            logger.warning("pygetwindow not available, skipping window monitoring")
            return
        
        while self.is_running:
            try:
                active_window = gw.getActiveWindow()
                
                if active_window and active_window != self.current_window:
                    # Window changed
                    old_window = self.current_window
                    self.current_window = active_window
                    
                    event = ContextEvent(
                        event_type=EventType.WINDOW_CHANGE,
                        timestamp=datetime.now(),
                        data={
                            "old_window": old_window.title if old_window else None,
                            "new_window": active_window.title,
                            "window_size": (active_window.width, active_window.height),
                            "window_position": (active_window.left, active_window.top)
                        },
                        priority=2
                    )
                    
                    await self._add_event(event)
                
                await asyncio.sleep(self.settings.context_check_interval)
                
            except Exception as e:
                logger.error("Window monitoring error", error=str(e))
                await asyncio.sleep(5)  # Wait longer on error
    
    async def _monitor_applications(self) -> None:
        """Monitor application changes"""
        if not psutil:
            logger.warning("psutil not available, skipping application monitoring")
            return
        
        while self.is_running:
            try:
                # Get current foreground process
                if platform.system() == "Windows":
                    # Windows-specific implementation would go here
                    pass
                
                # For now, we'll use a simple approach
                current_processes = {p.pid: p.name() for p in psutil.process_iter(['pid', 'name'])}
                
                # This is a simplified implementation
                # In a real scenario, you'd want to detect the actual foreground application
                
                await asyncio.sleep(self.settings.context_check_interval * 2)
                
            except Exception as e:
                logger.error("Application monitoring error", error=str(e))
                await asyncio.sleep(5)
    
    async def _monitor_screen_changes(self) -> None:
        """Monitor significant screen changes"""
        while self.is_running:
            try:
                # Capture current screen
                image = await self.screen_capture.capture_fullscreen()
                
                if image:
                    # Calculate simple hash for change detection
                    import hashlib
                    image_bytes = image.tobytes()
                    current_hash = hashlib.md5(image_bytes).hexdigest()
                    
                    if self.last_screen_hash and current_hash != self.last_screen_hash:
                        # Significant screen change detected
                        event = ContextEvent(
                            event_type=EventType.SCREEN_CHANGE,
                            timestamp=datetime.now(),
                            data={
                                "previous_hash": self.last_screen_hash,
                                "current_hash": current_hash,
                                "image_size": image.size
                            },
                            priority=1
                        )
                        
                        await self._add_event(event)
                    
                    self.last_screen_hash = current_hash
                
                await asyncio.sleep(self.settings.context_check_interval * 3)
                
            except Exception as e:
                logger.error("Screen monitoring error", error=str(e))
                await asyncio.sleep(10)
    
    async def _monitor_system(self) -> None:
        """Monitor system events"""
        while self.is_running:
            try:
                # Monitor system resources
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                # Check for high resource usage
                if cpu_percent > 80 or memory.percent > 90:
                    event = ContextEvent(
                        event_type=EventType.SYSTEM_EVENT,
                        timestamp=datetime.now(),
                        data={
                            "cpu_percent": cpu_percent,
                            "memory_percent": memory.percent,
                            "event": "high_resource_usage"
                        },
                        priority=2
                    )
                    
                    await self._add_event(event)
                
                await asyncio.sleep(self.settings.context_check_interval * 5)
                
            except Exception as e:
                logger.error("System monitoring error", error=str(e))
                await asyncio.sleep(10)
    
    async def _process_events(self) -> None:
        """Process accumulated events"""
        while self.is_running:
            try:
                # Process unprocessed events
                unprocessed_events = [e for e in self.events if not e.processed]
                
                if unprocessed_events:
                    # Sort by priority and timestamp
                    unprocessed_events.sort(
                        key=lambda e: (e.priority, e.timestamp), 
                        reverse=True
                    )
                    
                    # Process high-priority events
                    for event in unprocessed_events[:5]:  # Process max 5 events at a time
                        await self._handle_event(event)
                        event.processed = True
                
                # Clean up old events
                cutoff_time = datetime.now().timestamp() - 3600  # 1 hour
                self.events = [
                    e for e in self.events 
                    if e.timestamp.timestamp() > cutoff_time
                ]
                
                await asyncio.sleep(self.settings.context_check_interval)
                
            except Exception as e:
                logger.error("Event processing error", error=str(e))
                await asyncio.sleep(5)
    
    async def _add_event(self, event: ContextEvent) -> None:
        """Add event to the queue"""
        self.events.append(event)
        logger.debug("Event added", 
                    event_type=event.event_type.value, 
                    priority=event.priority)
        
        # Limit event queue size
        if len(self.events) > self.settings.max_context_history:
            self.events = self.events[-self.settings.max_context_history:]
    
    async def _handle_event(self, event: ContextEvent) -> None:
        """Handle a specific event"""
        try:
            logger.info("Processing event", 
                       event_type=event.event_type.value,
                       priority=event.priority)
            
            # Call registered handlers
            handlers = self.event_handlers.get(event.event_type, [])
            for handler in handlers:
                try:
                    await handler(event)
                except Exception as e:
                    logger.error("Event handler error", 
                               handler=handler.__name__, error=str(e))
            
            # Default handling based on event type
            if event.event_type == EventType.WINDOW_CHANGE:
                await self._handle_window_change(event)
            elif event.event_type == EventType.SCREEN_CHANGE:
                await self._handle_screen_change(event)
            
        except Exception as e:
            logger.error("Event handling error", error=str(e))
    
    async def _handle_window_change(self, event: ContextEvent) -> None:
        """Handle window change event"""
        logger.info("Window changed", 
                   old=event.data.get("old_window"),
                   new=event.data.get("new_window"))
    
    async def _handle_screen_change(self, event: ContextEvent) -> None:
        """Handle screen change event"""
        logger.debug("Screen content changed")
    
    def register_event_handler(
        self, 
        event_type: EventType, 
        handler: Callable[[ContextEvent], None]
    ) -> None:
        """Register an event handler"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.info("Event handler registered", 
                   event_type=event_type.value,
                   handler=handler.__name__)
    
    def get_recent_events(self, limit: int = 10) -> List[ContextEvent]:
        """Get recent events"""
        return sorted(self.events, key=lambda e: e.timestamp, reverse=True)[:limit]
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get context summary"""
        return {
            "is_running": self.is_running,
            "current_window": self.current_window.title if self.current_window else None,
            "total_events": len(self.events),
            "unprocessed_events": len([e for e in self.events if not e.processed]),
            "monitoring_tasks": len(self.monitoring_tasks),
            "last_update": datetime.now().isoformat()
        }
