"""
Enhanced Vision LLM Integration with Advanced Features
"""

import asyncio
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime

import structlog

from .providers import (
    ProviderManager, AnthropicProvider, OpenAIProvider,
    CustomProvider, ProviderConfig
)
from .optimization import ImageOptimizer, CompressionSettings, RequestBatcher, BatchConfig
from .resilience import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorContext, error_handler
from ..config.enhanced_settings import get_enhanced_settings, ProviderType

logger = structlog.get_logger(__name__)


class ConversationMessage:
    """Enhanced conversation message with metadata"""
    
    def __init__(self, 
                 role: str, 
                 content: str, 
                 image_data: Optional[str] = None, 
                 timestamp: Optional[datetime] = None,
                 provider_used: Optional[str] = None,
                 processing_time: Optional[float] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        self.role = role
        self.content = content
        self.image_data = image_data
        self.timestamp = timestamp or datetime.now()
        self.provider_used = provider_used
        self.processing_time = processing_time
        self.metadata = metadata or {}


class EnhancedVisionLLM:
    """Enhanced Vision LLM with comprehensive features"""
    
    def __init__(self):
        self.settings = get_enhanced_settings()
        self.provider_manager = ProviderManager(
            cache_ttl=self.settings.optimization.cache_ttl_seconds,
            max_cache_size=self.settings.optimization.max_cache_size
        )
        self.image_optimizer = ImageOptimizer(
            default_settings=CompressionSettings(
                max_width=self.settings.optimization.max_image_width,
                max_height=self.settings.optimization.max_image_height,
                quality=self.settings.optimization.image_quality,
                auto_format=self.settings.optimization.auto_format_selection
            )
        )
        self.error_handler = ErrorHandler()
        self.conversation_history: List[ConversationMessage] = []
        self.logger = logger.bind(component="enhanced_vision_llm")
        self._initialized = False
        
        # Request batcher (optional)
        self.request_batcher = None
        if self.settings.optimization.enable_batching:
            batch_config = BatchConfig(
                max_batch_size=self.settings.optimization.max_batch_size,
                max_wait_time=self.settings.optimization.max_batch_wait_time,
                enable_batching=True
            )
            self.request_batcher = RequestBatcher(batch_config, self._execute_single_request)
    
    async def initialize(self) -> None:
        """Initialize all providers and components"""
        if self._initialized:
            return
        
        try:
            await self._setup_providers()
            self._initialized = True
            self.logger.info("Enhanced Vision LLM initialized successfully")
            
        except Exception as e:
            self.logger.error("Failed to initialize Enhanced Vision LLM", error=str(e))
            raise
    
    async def _setup_providers(self) -> None:
        """Setup all configured providers"""
        # Anthropic provider
        if self.settings.anthropic.enabled and self.settings.anthropic.api_key:
            config = ProviderConfig(
                provider_type=ProviderType.ANTHROPIC,
                name="Anthropic Claude",
                api_key=self.settings.anthropic.api_key,
                model_name=self.settings.anthropic.model_name,
                max_tokens=self.settings.anthropic.max_tokens,
                timeout=self.settings.anthropic.timeout,
                max_retries=self.settings.anthropic.max_retries,
                retry_delay=self.settings.anthropic.retry_delay,
                rate_limit_requests=self.settings.anthropic.rate_limit_requests,
                rate_limit_window=self.settings.anthropic.rate_limit_window,
                priority=self.settings.anthropic.priority,
                health_check_interval=self.settings.anthropic.health_check_interval,
                circuit_breaker_threshold=self.settings.anthropic.circuit_breaker_threshold,
                circuit_breaker_timeout=self.settings.anthropic.circuit_breaker_timeout,
                custom_headers=self.settings.anthropic.custom_headers
            )
            provider = AnthropicProvider(config)
            await self.provider_manager.add_provider(provider)
        
        # OpenAI provider
        if self.settings.openai.enabled and self.settings.openai.api_key:
            config = ProviderConfig(
                provider_type=ProviderType.OPENAI,
                name="OpenAI GPT-4V",
                api_key=self.settings.openai.api_key,
                base_url=self.settings.openai.base_url,
                model_name=self.settings.openai.model_name,
                max_tokens=self.settings.openai.max_tokens,
                timeout=self.settings.openai.timeout,
                max_retries=self.settings.openai.max_retries,
                retry_delay=self.settings.openai.retry_delay,
                rate_limit_requests=self.settings.openai.rate_limit_requests,
                rate_limit_window=self.settings.openai.rate_limit_window,
                priority=self.settings.openai.priority,
                health_check_interval=self.settings.openai.health_check_interval,
                circuit_breaker_threshold=self.settings.openai.circuit_breaker_threshold,
                circuit_breaker_timeout=self.settings.openai.circuit_breaker_timeout,
                custom_headers=self.settings.openai.custom_headers
            )
            provider = OpenAIProvider(config)
            await self.provider_manager.add_provider(provider)
        
        # Custom provider
        if self.settings.custom.enabled and self.settings.custom.base_url:
            config = ProviderConfig(
                provider_type=ProviderType.CUSTOM,
                name=self.settings.custom.custom_headers.get("provider_name", "Custom Provider"),
                api_key=self.settings.custom.api_key,
                base_url=self.settings.custom.base_url,
                model_name=self.settings.custom.model_name,
                max_tokens=self.settings.custom.max_tokens,
                timeout=self.settings.custom.timeout,
                max_retries=self.settings.custom.max_retries,
                retry_delay=self.settings.custom.retry_delay,
                rate_limit_requests=self.settings.custom.rate_limit_requests,
                rate_limit_window=self.settings.custom.rate_limit_window,
                priority=self.settings.custom.priority,
                health_check_interval=self.settings.custom.health_check_interval,
                circuit_breaker_threshold=self.settings.custom.circuit_breaker_threshold,
                circuit_breaker_timeout=self.settings.custom.circuit_breaker_timeout,
                custom_headers=self.settings.custom.custom_headers
            )
            provider = CustomProvider(config)
            await self.provider_manager.add_provider(provider)
    
    @error_handler(operation_name="analyze_image", reraise=True)
    async def analyze_image(
        self,
        image_base64: str,
        prompt: str = "Analyze this image and describe what you see in detail.",
        preferred_provider: Optional[str] = None,
        optimize_image: bool = True,
        target_size_kb: Optional[int] = None,
        use_cache: bool = True,
        use_batching: bool = True,
        **kwargs
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Analyze image with comprehensive optimization and error handling
        
        Args:
            image_base64: Base64 encoded image
            prompt: Analysis prompt
            preferred_provider: Preferred provider name
            optimize_image: Whether to optimize image before sending
            target_size_kb: Target image size in KB
            use_cache: Whether to use caching
            use_batching: Whether to use request batching
            **kwargs: Additional parameters
            
        Returns:
            Tuple of (analysis_result, metadata)
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = datetime.now()
        metadata = {
            "original_image_size_kb": len(image_base64) * 3 / 4 / 1024,  # Approximate
            "optimization_applied": False,
            "provider_used": None,
            "processing_time_seconds": 0.0,
            "cache_hit": False,
            "batch_processed": False
        }
        
        try:
            # Optimize image if enabled
            optimized_image = image_base64
            if optimize_image and self.settings.optimization.enable_image_optimization:
                optimized_image, optimization_stats = self.image_optimizer.optimize_image_base64(
                    image_base64,
                    target_size_kb=target_size_kb
                )
                metadata["optimization_applied"] = True
                metadata["optimization_stats"] = optimization_stats
                
                self.logger.debug(
                    "Image optimized",
                    original_size_kb=optimization_stats.get("original_size_kb"),
                    optimized_size_kb=optimization_stats.get("optimized_size_kb"),
                    compression_ratio=optimization_stats.get("compression_ratio")
                )
            
            # Execute analysis
            if use_batching and self.request_batcher and self.settings.optimization.enable_batching:
                # Use batching
                result = await self.request_batcher.submit_request(
                    optimized_image, prompt, preferred_provider=preferred_provider, **kwargs
                )
                provider_used = "batched"  # We don't know which provider was used in batch
                metadata["batch_processed"] = True
            else:
                # Direct execution
                result, provider_used = await self.provider_manager.analyze_image(
                    optimized_image,
                    prompt,
                    preferred_provider=preferred_provider,
                    use_cache=use_cache,
                    **kwargs
                )
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            metadata.update({
                "provider_used": provider_used,
                "processing_time_seconds": processing_time
            })
            
            # Add to conversation history
            self._add_to_conversation("user", prompt, optimized_image, provider_used, processing_time, metadata)
            self._add_to_conversation("assistant", result, None, provider_used, processing_time)
            
            self.logger.info(
                "Image analysis completed successfully",
                provider=provider_used,
                processing_time=processing_time,
                prompt_length=len(prompt),
                result_length=len(result)
            )
            
            return result, metadata
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            metadata["processing_time_seconds"] = processing_time
            metadata["error"] = str(e)
            
            # Handle error with context
            context = ErrorContext(
                provider_name=preferred_provider,
                operation="analyze_image",
                additional_data=metadata
            )
            
            error_info = self.error_handler.handle_error(e, context)
            
            self.logger.error(
                "Image analysis failed",
                error_id=error_info.error_id,
                category=error_info.category.value,
                severity=error_info.severity.value,
                processing_time=processing_time
            )
            
            raise
    
    async def _execute_single_request(
        self,
        image_base64: str,
        prompt: str,
        preferred_provider: Optional[str] = None,
        **kwargs
    ) -> str:
        """Execute single request (used by batcher)"""
        result, _ = await self.provider_manager.analyze_image(
            image_base64,
            prompt,
            preferred_provider=preferred_provider,
            use_cache=True,
            **kwargs
        )
        return result
    
    def _add_to_conversation(
        self,
        role: str,
        content: str,
        image_data: Optional[str] = None,
        provider_used: Optional[str] = None,
        processing_time: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add message to conversation history"""
        message = ConversationMessage(
            role=role,
            content=content,
            image_data=image_data,
            provider_used=provider_used,
            processing_time=processing_time,
            metadata=metadata
        )
        
        self.conversation_history.append(message)
        
        # Limit conversation history size
        max_history = getattr(self.settings, 'max_context_history', 100)
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]
    
    async def get_conversation_context(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation context"""
        recent_messages = self.conversation_history[-limit:] if limit > 0 else self.conversation_history

        return [
            {
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat(),
                "provider_used": msg.provider_used,
                "processing_time": msg.processing_time,
                "has_image": msg.image_data is not None
            }
            for msg in recent_messages
        ]

    async def get_available_providers(self) -> List[Dict[str, Any]]:
        """Get list of available providers with their status"""
        if not self._initialized:
            await self.initialize()

        provider_status = await self.provider_manager.get_provider_status()
        return [
            {
                "name": name,
                "status": info["status"],
                "enabled": info["enabled"],
                "priority": info["priority"],
                "type": info["type"],
                "metrics": info["metrics"]
            }
            for name, info in provider_status["providers"].items()
        ]

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        if not self._initialized:
            await self.initialize()

        # Get provider status
        provider_status = await self.provider_manager.get_provider_status()

        # Get error statistics
        error_stats = self.error_handler.get_error_statistics(hours=24)

        # Get batching statistics
        batch_stats = {}
        if self.request_batcher:
            batch_stats = self.request_batcher.get_stats()

        # Get conversation statistics
        conversation_stats = {
            "total_messages": len(self.conversation_history),
            "user_messages": len([msg for msg in self.conversation_history if msg.role == "user"]),
            "assistant_messages": len([msg for msg in self.conversation_history if msg.role == "assistant"]),
            "messages_with_images": len([msg for msg in self.conversation_history if msg.image_data])
        }

        return {
            "initialized": self._initialized,
            "providers": provider_status,
            "error_statistics": error_stats,
            "batch_statistics": batch_stats,
            "conversation_statistics": conversation_stats,
            "settings": {
                "optimization_enabled": self.settings.optimization.enable_image_optimization,
                "caching_enabled": self.settings.optimization.enable_caching,
                "batching_enabled": self.settings.optimization.enable_batching
            }
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        if not self._initialized:
            try:
                await self.initialize()
            except Exception as e:
                return {
                    "healthy": False,
                    "error": f"Initialization failed: {str(e)}",
                    "providers": {}
                }

        # Check all providers
        provider_health = await self.provider_manager.health_check_all()

        # Overall health status
        healthy_providers = sum(1 for status in provider_health.values() if status)
        total_providers = len(provider_health)

        overall_healthy = healthy_providers > 0  # At least one provider must be healthy

        return {
            "healthy": overall_healthy,
            "providers": provider_health,
            "healthy_provider_count": healthy_providers,
            "total_provider_count": total_providers,
            "error_rate_24h": self.error_handler.get_error_statistics(hours=24).get("error_rate_per_hour", 0),
            "last_check": datetime.now().isoformat()
        }

    async def clear_conversation_history(self) -> None:
        """Clear conversation history"""
        self.conversation_history.clear()
        self.logger.info("Conversation history cleared")

    async def get_optimization_recommendations(self, image_base64: str) -> Dict[str, Any]:
        """Get optimization recommendations for an image"""
        return self.image_optimizer.get_optimization_recommendations(image_base64)

    async def estimate_api_costs(self, image_base64: str) -> Dict[str, Any]:
        """Estimate API costs for all available providers"""
        if not self._initialized:
            await self.initialize()

        available_providers = self.provider_manager.get_available_providers()
        cost_estimates = {}

        for provider_name in available_providers:
            # Map provider names to cost estimation names
            cost_provider_name = provider_name.lower()
            if "anthropic" in cost_provider_name or "claude" in cost_provider_name:
                cost_provider_name = "anthropic"
            elif "openai" in cost_provider_name or "gpt" in cost_provider_name:
                cost_provider_name = "openai"
            elif "gemini" in cost_provider_name or "google" in cost_provider_name:
                cost_provider_name = "google"
            else:
                cost_provider_name = "custom"

            cost_estimates[provider_name] = self.image_optimizer.estimate_api_cost(
                image_base64, cost_provider_name
            )

        return cost_estimates

    async def cleanup(self) -> None:
        """Cleanup all resources"""
        try:
            # Cleanup provider manager
            await self.provider_manager.cleanup()

            # Cleanup request batcher
            if self.request_batcher:
                await self.request_batcher.cleanup()

            # Clear conversation history
            self.conversation_history.clear()

            # Clear error history
            self.error_handler.clear_history()

            self._initialized = False
            self.logger.info("Enhanced Vision LLM cleanup completed")

        except Exception as e:
            self.logger.error("Error during cleanup", error=str(e))

    def get_model_info(self) -> Dict[str, Any]:
        """Get detailed model information for all providers"""
        if not self._initialized:
            return {"error": "System not initialized"}

        model_info = {}
        for provider_name, provider in self.provider_manager.providers.items():
            if hasattr(provider, 'get_model_info'):
                model_info[provider_name] = provider.get_model_info()

        return model_info
