"""
Screen Capture Module for Vision Context MCP Server
"""

import asyncio
import os
import platform
import subprocess
import tempfile
from typing import Optional, Tuple

import cv2
import mss
import numpy as np
import structlog
from PIL import Image

try:
    import pygetwindow as gw
except ImportError:
    gw = None

try:
    import pyautogui
except ImportError:
    pyautogui = None

from ..config.enhanced_settings import get_enhanced_settings

logger = structlog.get_logger(__name__)


class ScreenCapture:
    """Screen capture functionality"""
    
    def __init__(self):
        self.settings = get_enhanced_settings()
        self.system = platform.system()
        logger.info("Screen capture initialized", system=self.system)
    
    async def capture_fullscreen(self) -> Optional[Image.Image]:
        """
        Capture full screen
        
        Returns:
            PIL Image or None if capture fails
        """
        try:
            with mss.mss() as sct:
                # Capture all monitors
                monitor = sct.monitors[0]  # All monitors combined
                screenshot = sct.grab(monitor)
                
                # Convert to PIL Image
                image = Image.frombytes(
                    "RGB", 
                    screenshot.size, 
                    screenshot.bgra, 
                    "raw", 
                    "BGRX"
                )
                
                # Resize if needed
                if self.settings.max_image_size:
                    image.thumbnail(self.settings.max_image_size, Image.Resampling.LANCZOS)
                
                logger.info("Fullscreen captured", size=image.size)
                return image
                
        except Exception as e:
            logger.error("Fullscreen capture failed", error=str(e))
            return None
    
    async def capture_active_window(self) -> Optional[Image.Image]:
        """
        Capture active window
        
        Returns:
            PIL Image or None if capture fails
        """
        try:
            if self.system == "Windows":
                return await self._capture_active_window_windows()
            elif self.system == "Darwin":  # macOS
                return await self._capture_active_window_macos()
            elif self.system == "Linux":
                return await self._capture_active_window_linux()
            else:
                logger.warning("Unsupported system for active window capture", system=self.system)
                return await self.capture_fullscreen()
                
        except Exception as e:
            logger.error("Active window capture failed", error=str(e))
            # Fallback to fullscreen
            return await self.capture_fullscreen()
    
    async def _capture_active_window_windows(self) -> Optional[Image.Image]:
        """Capture active window on Windows"""
        if not gw:
            logger.warning("pygetwindow not available, falling back to fullscreen")
            return await self.capture_fullscreen()
        
        try:
            # Get active window
            active_window = gw.getActiveWindow()
            if not active_window:
                logger.warning("No active window found")
                return await self.capture_fullscreen()
            
            # Get window bounds
            left, top, width, height = (
                active_window.left,
                active_window.top,
                active_window.width,
                active_window.height
            )
            
            # Capture window area
            with mss.mss() as sct:
                monitor = {
                    "top": top,
                    "left": left,
                    "width": width,
                    "height": height
                }
                screenshot = sct.grab(monitor)
                
                # Convert to PIL Image
                image = Image.frombytes(
                    "RGB",
                    screenshot.size,
                    screenshot.bgra,
                    "raw",
                    "BGRX"
                )
                
                # Resize if needed
                if self.settings.max_image_size:
                    image.thumbnail(self.settings.max_image_size, Image.Resampling.LANCZOS)
                
                logger.info("Active window captured", 
                           window=active_window.title, size=image.size)
                return image
                
        except Exception as e:
            logger.error("Windows active window capture failed", error=str(e))
            return None
    
    async def _capture_active_window_macos(self) -> Optional[Image.Image]:
        """Capture active window on macOS"""
        try:
            # Use screencapture command to get active window
            temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
            temp_file.close()
            
            # Capture active window
            process = await asyncio.create_subprocess_exec(
                "screencapture", "-w", temp_file.name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            if process.returncode == 0 and os.path.exists(temp_file.name):
                image = Image.open(temp_file.name)
                
                # Resize if needed
                if self.settings.max_image_size:
                    image.thumbnail(self.settings.max_image_size, Image.Resampling.LANCZOS)
                
                # Clean up
                os.unlink(temp_file.name)
                
                logger.info("macOS active window captured", size=image.size)
                return image
            else:
                logger.error("screencapture command failed")
                return None
                
        except Exception as e:
            logger.error("macOS active window capture failed", error=str(e))
            return None
    
    async def _capture_active_window_linux(self) -> Optional[Image.Image]:
        """Capture active window on Linux"""
        try:
            # Use xwininfo and import commands
            # First get active window ID
            process = await asyncio.create_subprocess_exec(
                "xprop", "-root", "_NET_ACTIVE_WINDOW",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error("Failed to get active window ID")
                return None
            
            # Extract window ID
            output = stdout.decode().strip()
            window_id = output.split()[-1]
            
            # Capture window
            temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
            temp_file.close()
            
            process = await asyncio.create_subprocess_exec(
                "import", "-window", window_id, temp_file.name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            if process.returncode == 0 and os.path.exists(temp_file.name):
                image = Image.open(temp_file.name)
                
                # Resize if needed
                if self.settings.max_image_size:
                    image.thumbnail(self.settings.max_image_size, Image.Resampling.LANCZOS)
                
                # Clean up
                os.unlink(temp_file.name)
                
                logger.info("Linux active window captured", size=image.size)
                return image
            else:
                logger.error("import command failed")
                return None
                
        except Exception as e:
            logger.error("Linux active window capture failed", error=str(e))
            return None

    async def record_screen(
        self,
        duration: int,
        output_path: str,
        mode: str = "fullscreen",
        fps: int = None
    ) -> bool:
        """
        Record screen for specified duration

        Args:
            duration: Recording duration in seconds
            output_path: Output video file path
            mode: Recording mode ("fullscreen" or "active_window")
            fps: Frames per second (uses settings default if None)

        Returns:
            True if recording successful
        """
        if fps is None:
            fps = self.settings.video_fps

        try:
            logger.info("Starting screen recording",
                       duration=duration, mode=mode, fps=fps)

            # Get screen dimensions
            if mode == "fullscreen":
                with mss.mss() as sct:
                    monitor = sct.monitors[0]
                    width, height = monitor["width"], monitor["height"]
                    capture_area = monitor
            else:  # active_window
                if self.system == "Windows" and gw:
                    active_window = gw.getActiveWindow()
                    if active_window:
                        width, height = active_window.width, active_window.height
                        capture_area = {
                            "top": active_window.top,
                            "left": active_window.left,
                            "width": width,
                            "height": height
                        }
                    else:
                        # Fallback to fullscreen
                        with mss.mss() as sct:
                            monitor = sct.monitors[0]
                            width, height = monitor["width"], monitor["height"]
                            capture_area = monitor
                else:
                    # Fallback to fullscreen for other systems
                    with mss.mss() as sct:
                        monitor = sct.monitors[0]
                        width, height = monitor["width"], monitor["height"]
                        capture_area = monitor

            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not video_writer.isOpened():
                logger.error("Failed to open video writer")
                return False

            # Record frames
            frame_count = 0
            target_frames = duration * fps

            with mss.mss() as sct:
                start_time = asyncio.get_event_loop().time()

                while frame_count < target_frames:
                    # Capture frame
                    screenshot = sct.grab(capture_area)

                    # Convert to numpy array
                    frame = np.array(screenshot)
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)

                    # Write frame
                    video_writer.write(frame)
                    frame_count += 1

                    # Control frame rate
                    elapsed = asyncio.get_event_loop().time() - start_time
                    expected_time = frame_count / fps

                    if elapsed < expected_time:
                        await asyncio.sleep(expected_time - elapsed)

            # Release video writer
            video_writer.release()

            logger.info("Screen recording completed",
                       frames=frame_count, output=output_path)
            return True

        except Exception as e:
            logger.error("Screen recording failed", error=str(e))
            return False

    async def extract_frame_from_video(
        self,
        video_path: str,
        timestamp_sec: float = 0
    ) -> Optional[Image.Image]:
        """
        Extract a frame from video at specified timestamp

        Args:
            video_path: Path to video file
            timestamp_sec: Timestamp in seconds

        Returns:
            PIL Image or None if extraction fails
        """
        try:
            cap = cv2.VideoCapture(video_path)

            if not cap.isOpened():
                logger.error("Failed to open video file", path=video_path)
                return None

            # Set frame position
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_number = int(timestamp_sec * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

            # Read frame
            ret, frame = cap.read()
            cap.release()

            if not ret:
                logger.error("Failed to read frame from video")
                return None

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Convert to PIL Image
            image = Image.fromarray(frame_rgb)

            # Resize if needed
            if self.settings.max_image_size:
                image.thumbnail(self.settings.max_image_size, Image.Resampling.LANCZOS)

            logger.info("Frame extracted from video",
                       timestamp=timestamp_sec, size=image.size)
            return image

        except Exception as e:
            logger.error("Frame extraction failed", error=str(e))
            return None
